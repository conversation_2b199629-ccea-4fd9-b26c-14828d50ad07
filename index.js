const axios = require('axios');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// 分类ID到名称的映射
const CATEGORY_NAMES = {
    9: 'General Knowledge',
    10: 'Entertainment: Books',
    11: 'Entertainment: Film',
    12: 'Entertainment: Music',
    13: 'Entertainment: Musicals & Theatres',
    14: 'Entertainment: Television',
    15: 'Entertainment: Video Games',
    16: 'Entertainment: Board Games',
    17: 'Science & Nature',
    18: 'Science: Computers',
    19: 'Science: Mathematics',
    20: 'Mythology',
    21: 'Sports',
    22: 'Geography',
    23: 'History',
    24: 'Politics',
    25: 'Art',
    26: 'Celebrities',
    27: 'Animals',
    28: 'Vehicles',
    29: 'Entertainment: Comics',
    30: 'Science: Gadgets',
    31: 'Entertainment: Japanese Anime & Manga',
    32: 'Entertainment: Cartoon & Animations',
    33: 'Entertainment: Animated & Cartoon',
    34: 'Entertainment: Video Games'
};

// 从环境变量获取配置
const config = {
    baseUrl: process.env.HTTP_URL,
    amount: process.env.AMOUNT,
    difficulty: process.env.DIFFICULTY.replace(/"/g, ''), // 移除引号
    type: process.env.TYPE.replace(/"/g, ''), // 移除引号
    categories: process.env.CATEGORY_ALL
        .replace(/[\[\]]/g, '') // 移除方括号
        .split(',')
        .map(id => parseInt(id.trim()))
};

console.log('配置信息:', config);

// 创建输出目录（如果不存在）
const outputDir = './data';
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
}

// 请求单个分类的数据（带重试机制）
async function fetchCategoryData(categoryId, retryCount = 0) {
    const maxRetries = 3;

    try {
        const url = `${config.baseUrl}?amount=${config.amount}&category=${categoryId}&difficulty=${config.difficulty}&type=${config.type}`;
        console.log(`正在请求分类 ${categoryId} (${CATEGORY_NAMES[categoryId] || 'Unknown'}): ${url}`);

        const response = await axios.get(url);

        if (response.data.response_code === 0) {
            console.log(`✓ 成功获取分类 ${categoryId} 的 ${response.data.results.length} 条数据`);
            // 处理每个题目，将正确答案随机插入到选项中
            const processedResults = response.data.results.map(question => {
                const allAnswers = [...question.incorrect_answers];
                // 随机位置插入正确答案
                const randomIndex = Math.floor(Math.random() * (allAnswers.length + 1));
                allAnswers.splice(randomIndex, 0, question.correct_answer);

                return {
                    ...question,
                    all_answers: allAnswers, // 包含所有选项的数组
                    correct_answer_index: randomIndex // 正确答案在all_answers中的索引
                };
            });
            return processedResults;
        } else {
            console.error(`✗ 分类 ${categoryId} 请求失败，响应码: ${response.data.response_code}`);
            return [];
        }
    } catch (error) {
        if (error.response && error.response.status === 429 && retryCount < maxRetries) {
            const waitTime = (retryCount + 1) * 5000; // 5秒, 10秒, 15秒
            console.log(`⏳ 分类 ${categoryId} 遇到速率限制，${waitTime/1000}秒后重试 (${retryCount + 1}/${maxRetries})`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
            return fetchCategoryData(categoryId, retryCount + 1);
        } else {
            console.error(`✗ 分类 ${categoryId} 请求出错:`, error.message);
            return [];
        }
    }
}

// 主函数
async function main() {
    console.log('开始获取题目数据...\n');
    
    const allData = {};
    
    // 循环请求所有分类
    for (const categoryId of config.categories) {
        const categoryName = CATEGORY_NAMES[categoryId] || `Category ${categoryId}`;
        const data = await fetchCategoryData(categoryId);
        
        if (data.length > 0) {
            allData[categoryName] = data;
        }
        
        // 添加延迟以避免请求过于频繁（增加到2秒）
        await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    // 保存数据到文件
    const outputPath = path.join(outputDir, 'data.json');
    try {
        fs.writeFileSync(outputPath, JSON.stringify(allData, null, 2), 'utf8');
        console.log(`\n✓ 数据已保存到: ${outputPath}`);
        console.log(`✓ 总共获取了 ${Object.keys(allData).length} 个分类的数据`);
        
        // 显示每个分类的数据数量
        Object.entries(allData).forEach(([categoryName, questions]) => {
            console.log(`  - ${categoryName}: ${questions.length} 条题目`);
        });
        
    } catch (error) {
        console.error('✗ 保存文件时出错:', error.message);
    }
}

// 运行主函数
main().catch(error => {
    console.error('程序执行出错:', error.message);
    process.exit(1);
});
