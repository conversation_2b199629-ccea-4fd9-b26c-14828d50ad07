{"General Knowledge": [{"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "In Half-Life, what is the name of the alien that attaches to heads?", "correct_answer": "Headcrab", "incorrect_answers": ["Bullsquid", "Vortigaunt", "<PERSON><PERSON><PERSON>"], "all_answers": ["Bullsquid", "Headcrab", "Vortigaunt", "<PERSON><PERSON><PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "A criminal who has &ldquo;cooked the books&rdquo; has engaged in what kind of illegal behavior?", "correct_answer": "Accounting Fraud", "incorrect_answers": ["Money Laundering", "Extortion", "Blackmail"], "all_answers": ["Money Laundering", "Accounting Fraud", "Extortion", "Blackmail"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "What does a funambulist walk on?", "correct_answer": "A Tight Rope", "incorrect_answers": ["Broken Glass", "Balls", "The Moon"], "all_answers": ["Broken Glass", "Balls", "The Moon", "A Tight Rope"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "The Canadian $1 coin is colloquially known as a what?", "correct_answer": "<PERSON><PERSON><PERSON>", "incorrect_answers": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "all_answers": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "What nuts are used in the production of marzipan?", "correct_answer": "Almonds", "incorrect_answers": ["Peanuts", "Walnuts", "Pistachios"], "all_answers": ["Peanuts", "Walnuts", "Pistachios", "Almonds"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "Which of these Marvel games was released on the Playstation 2?", "correct_answer": "Spider-Man 2", "incorrect_answers": ["Silver Surfer", "<PERSON> the Duck", "Wolverine: Adamantium Rage"], "all_answers": ["Silver Surfer", "<PERSON> the Duck", "Wolverine: Adamantium Rage", "Spider-Man 2"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "What is the name of Poland in Polish?", "correct_answer": "Polska", "incorrect_answers": ["<PERSON><PERSON><PERSON>", "Polszka", "P&oacute;land"], "all_answers": ["Polska", "<PERSON><PERSON><PERSON>", "Polszka", "P&oacute;land"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "What is the Spanish word for &quot;donkey&quot;?", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["Caballo", "Toro", "<PERSON><PERSON>"], "all_answers": ["<PERSON><PERSON>", "Caballo", "Toro", "<PERSON><PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "Which sign of the zodiac comes between Virgo and Scorpio?", "correct_answer": "Libra", "incorrect_answers": ["Gemini", "<PERSON><PERSON>", "Capricorn"], "all_answers": ["Gemini", "Libra", "<PERSON><PERSON>", "Capricorn"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "In DC comics where does the <PERSON> Arrow (<PERSON>) live?", "correct_answer": "Star City", "incorrect_answers": ["Central City", "Gotham City", "Metropolis"], "all_answers": ["Star City", "Central City", "Gotham City", "Metropolis"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "What style of beer will typically have a higher than average hop content?", "correct_answer": "India Pale Ale", "incorrect_answers": ["Stout", "Extra Special Bitter", "Scotch Ale"], "all_answers": ["Stout", "Extra Special Bitter", "India Pale Ale", "Scotch Ale"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "What alcoholic drink is made from molasses?", "correct_answer": "Rum", "incorrect_answers": ["Gin", "Vodka", "Whisky"], "all_answers": ["Rum", "Gin", "Vodka", "Whisky"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "<PERSON><PERSON><PERSON><PERSON>&#039;s first appearance was in what game?", "correct_answer": "Mario Tennis 64 (N64)", "incorrect_answers": ["Wario Land: Super Mario Land 3", "<PERSON> (N64)", "Super Smash Bros. Ultimate"], "all_answers": ["Wario Land: Super Mario Land 3", "<PERSON> (N64)", "Mario Tennis 64 (N64)", "Super Smash Bros. Ultimate"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "Which of the following landmarks is not located in New York City?", "correct_answer": "Lincoln Memorial", "incorrect_answers": ["Empire State Building", "Times Square", "Central Park"], "all_answers": ["Empire State Building", "Times Square", "Lincoln Memorial", "Central Park"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "Which of the following blood component forms a plug at the site of injuries?", "correct_answer": "Platelets", "incorrect_answers": ["Red blood cells", "White blood cells", "Blood plasma"], "all_answers": ["Red blood cells", "White blood cells", "Blood plasma", "Platelets"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "Which one of these is not a typical European sword design?", "correct_answer": "Scimitar", "incorrect_answers": ["Falchion", "<PERSON><PERSON><PERSON><PERSON>", "Flamberge"], "all_answers": ["Falchion", "<PERSON><PERSON><PERSON><PERSON>", "Scimitar", "Flamberge"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "What is the name of the main Character from the Series: Dead Space?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "Commander <PERSON>", "Captain <PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "Commander <PERSON>", "Captain <PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "What was the first ever London Underground line to be built?", "correct_answer": "Metropolitan Line", "incorrect_answers": ["Circle Line", "Bakerloo Line", "Victoria Line"], "all_answers": ["Circle Line", "Metropolitan Line", "Bakerloo Line", "Victoria Line"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": " Which chemical element, number 11 in the Periodic table, has the symbol Na?", "correct_answer": "Sodium", "incorrect_answers": ["Carbon", "Lead", "Nitrogen"], "all_answers": ["Sodium", "Carbon", "Lead", "Nitrogen"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "Five dollars is worth how many nickels?", "correct_answer": "100", "incorrect_answers": ["50", "25", "69"], "all_answers": ["50", "100", "25", "69"], "correct_answer_index": 1}], "Entertainment: Books": [{"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "What is the name of <PERSON>&#039;s brother?", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "all_answers": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "Which classic book opens with the line &quot;Call me <PERSON><PERSON><PERSON>&quot;?", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["A Tale of Two Cities", "Kidnapped", "Wuthering Heights"], "all_answers": ["A Tale of Two Cities", "Kidnapped", "Wuthering Heights", "<PERSON><PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "What is the name of the protagonist of <PERSON><PERSON><PERSON><PERSON>&#039;s novel Catcher in the Rye?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "What&#039;s <PERSON>&#039;s dad&#039;s name?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> Sr."], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> Sr."], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "Who wrote &quot;A Tale of Two Cities&quot;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "What is the name of the three headed dog in <PERSON> and the Sorcerer&#039;s <PERSON>?", "correct_answer": "<PERSON><PERSON><PERSON>", "incorrect_answers": ["Spike", "Poofy", "Spot"], "all_answers": ["Spike", "Poofy", "Spot", "<PERSON><PERSON><PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "Which famous book is sub-titled &#039;The Modern Prometheus&#039;?", "correct_answer": "Frankenstein", "incorrect_answers": ["Dracula", "The Strange Case of Dr<PERSON> and Mr. <PERSON> ", "The Legend of Sleepy Hollow"], "all_answers": ["Dracula", "The Strange Case of Dr<PERSON> and Mr. <PERSON> ", "Frankenstein", "The Legend of Sleepy Hollow"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "How many Harry Potter books are there?", "correct_answer": "7", "incorrect_answers": ["8", "5", "6"], "all_answers": ["7", "8", "5", "6"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "Who was the author of the 1954 novel, &quot;Lord of the Flies&quot;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "Which is NOT a book in the Harry Potter Series?", "correct_answer": "The House Elf", "incorrect_answers": ["The Chamber of Secrets", "The Prisoner of Azkaban", "The Deathly Hallows"], "all_answers": ["The Chamber of Secrets", "The Prisoner of Azkaban", "The House Elf", "The Deathly Hallows"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "What is the title of the first Sherlock Holmes book by <PERSON>?", "correct_answer": "A Study in Scarlet", "incorrect_answers": ["The Sign of the Four", "A Case of Identity", "The Doings of <PERSON><PERSON>w"], "all_answers": ["The Sign of the Four", "A Study in Scarlet", "A Case of Identity", "The Doings of <PERSON><PERSON>w"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "What was Sir <PERSON>&#039;s original name in &quot;The Railway Series&quot; and it&#039;s animated counterpart &quot;Thomas and Friends?&quot;", "correct_answer": "Falcon", "incorrect_answers": ["Eagle", "Kyte", "S<PERSON>ow"], "all_answers": ["Eagle", "Falcon", "Kyte", "S<PERSON>ow"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "In what <PERSON> book was a Horcrux first encountered?", "correct_answer": "The Chamber of Secrets", "incorrect_answers": ["The Half-Blood Prince", "The Goblet of Fire", "The Deathly Hallows"], "all_answers": ["The Half-Blood Prince", "The Chamber of Secrets", "The Goblet of Fire", "The Deathly Hallows"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "Under what pseudonym did <PERSON> publish five novels between 1977 and 1984?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "What was the name of Captain <PERSON><PERSON><PERSON>&#039;s submarine in &quot;20,000 Leagues Under the Sea&quot;?", "correct_answer": "The Nautilus", "incorrect_answers": ["The Neptune", "The Poseidon  ", "The Atlantis"], "all_answers": ["The Nautilus", "The Neptune", "The Poseidon  ", "The Atlantis"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "Who wrote the novel &#039;Fear And Loathing In Las Vegas&#039;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "Which of the following is the world&#039;s best-selling book?", "correct_answer": "The Lord of the Rings", "incorrect_answers": ["The Little Prince", "<PERSON> and the Philosopher&#039;s <PERSON>", "The Da Vinci Code"], "all_answers": ["The Little Prince", "<PERSON> and the Philosopher&#039;s <PERSON>", "The Lord of the Rings", "The Da Vinci Code"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "Who wrote the young adult novel &quot;The Fault in Our Stars&quot;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "<PERSON> wrote this book, which is often considered a statement on government oversight.", "correct_answer": "1984", "incorrect_answers": ["The Old Man and the Sea", "Catcher and the Rye", "To Kill a Mockingbird"], "all_answers": ["The Old Man and the Sea", "1984", "Catcher and the Rye", "To Kill a Mockingbird"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "Who was the original author of Frankenstein?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "correct_answer_index": 1}], "Entertainment: Film": [{"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "Which of the following is not the name of a &quot;Bond Girl&quot;? ", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "Who wrote and directed the 1986 film &#039;Platoon&#039;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "What was the first James Bond film?", "correct_answer": "Dr. No", "incorrect_answers": ["Goldfinger", "From Russia With Love", "Thunderball"], "all_answers": ["Goldfinger", "From Russia With Love", "Dr. No", "Thunderball"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "The 1996 film &#039;Fargo&#039; is primarily set in which US state?", "correct_answer": "Minnesota", "incorrect_answers": ["North Dakota", "South Dakota", "Wisconsin"], "all_answers": ["North Dakota", "South Dakota", "Minnesota", "Wisconsin"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "What historical time period was the center of the Assassin&rsquo;s Creed movie (2016)?", "correct_answer": "Spanish Inquisition", "incorrect_answers": ["Victorian England", "French Revolution", "Colonial America"], "all_answers": ["Victorian England", "French Revolution", "Spanish Inquisition", "Colonial America"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "What was the first monster to appear alongside <PERSON><PERSON>?", "correct_answer": "<PERSON><PERSON><PERSON>", "incorrect_answers": ["King Kong", "<PERSON><PERSON><PERSON>", "King <PERSON><PERSON><PERSON><PERSON>"], "all_answers": ["King Kong", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "King <PERSON><PERSON><PERSON><PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "Who played the female lead in the 1933 film &quot;King Kong&quot;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "Who plays the character of <PERSON> in the Kung Fu Panda movies?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON> Ramses", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON> Ramses", "<PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "What is the orange and white bot&#039;s name in &quot;Star Wars: The Force Awakens&quot;?", "correct_answer": "BB-8", "incorrect_answers": ["BB-3", "AA-A", "R2-D2"], "all_answers": ["BB-3", "AA-A", "BB-8", "R2-D2"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "Which of the following actors has only been in a <PERSON> directed film once?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "In the 1986 film &quot;The Wraith&quot;, what concept car is used for the Turbo Interceptor?", "correct_answer": "Dodge M4S", "incorrect_answers": ["Pontiac Banshee", "General Motors Firebird", "Dodge Copperhead"], "all_answers": ["Pontiac Banshee", "Dodge M4S", "General Motors Firebird", "Dodge Copperhead"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "What was the first feature-length computer-animated movie?", "correct_answer": "Toy Story", "incorrect_answers": ["Tron", "Lion king", "101 Dalmatians"], "all_answers": ["Toy Story", "Tron", "Lion king", "101 Dalmatians"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "<PERSON> became a global star in the film industry due to his performance in which film franchise?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "Spy Kids", "Pirates of the Caribbean "], "all_answers": ["<PERSON>", "<PERSON>", "Spy Kids", "Pirates of the Caribbean "], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "Which film has been critically regarded as the best film of all time?", "correct_answer": "<PERSON>", "incorrect_answers": ["Avatar", "The Godfather Part II", "The Room"], "all_answers": ["Avatar", "The Godfather Part II", "The Room", "<PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "Who starred in the film 1973 movie &quot;<PERSON>ter The Dragon&quot;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "Jet Li", " <PERSON><PERSON><PERSON>"], "all_answers": ["<PERSON>", "Jet Li", "<PERSON>", " <PERSON><PERSON><PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "Who is frozen at the end of the movie &quot;Goldeneye&quot;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "Which of these films is NOT set in Los Angeles?", "correct_answer": "RoboCop", "incorrect_answers": ["Blade Runner", "The Terminator", "Predator 2"], "all_answers": ["Blade Runner", "The Terminator", "RoboCop", "Predator 2"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "Which movie includes a giant bunny-like spirit who has magic powers including growing trees?", "correct_answer": "My Neighbor Totoro", "incorrect_answers": ["Hop", "Rise of the Guardians", "Alice in Wonderland"], "all_answers": ["My Neighbor Totoro", "Hop", "Rise of the Guardians", "Alice in Wonderland"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "This movie contains the quote, &quot;<PERSON> puts <PERSON> in a corner.&quot;", "correct_answer": "Dirty Dancing", "incorrect_answers": ["Three Men and a Baby", "Ferris Bueller&#039;s Day Off", "Pretty in Pink"], "all_answers": ["Three Men and a Baby", "Dirty Dancing", "Ferris Bueller&#039;s Day Off", "Pretty in Pink"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "Which of the following was not one of &quot;The Magnificent Seven&quot;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 1}], "Entertainment: Music": [{"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "Which T<PERSON> streamer is the vocalist for Red Vox?", "correct_answer": "Vinesauce", "incorrect_answers": ["The8BitDrummer", "LIRIK", "Sodapop<PERSON>"], "all_answers": ["The8BitDrummer", "LIRIK", "Vinesauce", "Sodapop<PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "Which former boy-band star released hit solo single &quot;Angels&quot; in 1997?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "Which song on Daft Punk&#039;s &quot;Random Access Memories&quot; features <PERSON><PERSON><PERSON>?", "correct_answer": "Get Lucky", "incorrect_answers": ["Doin&#039; It Right", "Instant Crush", "The Game of Love"], "all_answers": ["Doin&#039; It Right", "Get Lucky", "Instant Crush", "The Game of Love"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "Which of these is the name of a song by Tears for Fears?", "correct_answer": "Shout", "incorrect_answers": ["Scream", "Yell", "<PERSON><PERSON>"], "all_answers": ["Shout", "Scream", "Yell", "<PERSON><PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "Which term best describes the connection of two sections of a musical piece through a transitional passage?", "correct_answer": "Bridge", "incorrect_answers": ["Coda", "<PERSON><PERSON>", "Binary Form"], "all_answers": ["Coda", "<PERSON><PERSON>", "Bridge", "Binary Form"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "Which of these is NOT the name of an album released by English singer-songwriter <PERSON>?", "correct_answer": "12", "incorrect_answers": ["19", "21", "25"], "all_answers": ["19", "21", "12", "25"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "What album did Bon Iver release in 2016?", "correct_answer": "22, A Million", "incorrect_answers": ["<PERSON>, <PERSON>", "Blood Bank EP", "For <PERSON>, <PERSON> Ago"], "all_answers": ["22, A Million", "<PERSON>, <PERSON>", "Blood Bank EP", "For <PERSON>, <PERSON> Ago"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "In an orchestra, what is the lowest member of the brass family?", "correct_answer": "Tuba", "incorrect_answers": ["Trom<PERSON>", "Contrabass", "Bassoon"], "all_answers": ["Trom<PERSON>", "Contrabass", "Bassoon", "Tuba"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "Where does the Mac part of the name <PERSON> come from?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "What was the best selling album of 2015?", "correct_answer": "<PERSON>, 25", "incorrect_answers": ["<PERSON><PERSON>, <PERSON><PERSON>", "<PERSON>, 1989", "<PERSON>, <PERSON><PERSON><PERSON>"], "all_answers": ["<PERSON><PERSON>, <PERSON><PERSON>", "<PERSON>, 25", "<PERSON>, 1989", "<PERSON>, <PERSON><PERSON><PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "What was Rage Against the Machine&#039;s debut album?", "correct_answer": "Rage Against the Machine", "incorrect_answers": ["Evil Empire", "Bombtrack", "The Battle Of Los Angeles"], "all_answers": ["Rage Against the Machine", "Evil Empire", "Bombtrack", "The Battle Of Los Angeles"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "What is the best selling album of all time from 1976 to 2018?", "correct_answer": "Thriller", "incorrect_answers": ["Back in Black", "Abbey Road", "The Dark Side of the Moon"], "all_answers": ["Back in Black", "Abbey Road", "The Dark Side of the Moon", "Thriller"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "Which group performs the song &quot;Crash into Me&quot;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON><PERSON>", "The Grateful Dead", "Destiny&#039;s <PERSON>"], "all_answers": ["<PERSON><PERSON>", "The Grateful Dead", "<PERSON>", "Destiny&#039;s <PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "What collaborative album was released by Ka<PERSON>e <PERSON> and Jay-Z in 2011?", "correct_answer": "Watch the Throne", "incorrect_answers": ["Distant Relatives", "What a Time to be Alive", "Unfinished Business"], "all_answers": ["Distant Relatives", "What a Time to be Alive", "Unfinished Business", "Watch the Throne"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "From which country did the song &quot;Gangnam Style&quot; originate from?", "correct_answer": "South Korea", "incorrect_answers": ["Japan", "North Korea", "China"], "all_answers": ["Japan", "South Korea", "North Korea", "China"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "Which rap group released the album &quot;Straight Outta Compton&quot;?", "correct_answer": "N.W.A", "incorrect_answers": ["Wu-Tang Clan", "Run-D.M.C.", "Beastie Boys"], "all_answers": ["Wu-Tang Clan", "Run-D.M.C.", "N.W.A", "Beastie Boys"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "Which brass instrument has the lowest pitch in an orchestra?", "correct_answer": "Tuba", "incorrect_answers": ["Trumpet", "Saxophone", "Trom<PERSON>"], "all_answers": ["Tuba", "Trumpet", "Saxophone", "Trom<PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "What is the name of the album released in 2014 by American band Maroon 5?", "correct_answer": "V", "incorrect_answers": ["X", "III", "IV"], "all_answers": ["X", "III", "IV", "V"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "Which of these are NOT a Men at Work song?", "correct_answer": "Basket Case", "incorrect_answers": ["Dr. <PERSON> and Mr. <PERSON><PERSON>", "Who Can It Be Now?", "<PERSON> Good Johnny"], "all_answers": ["Dr. <PERSON> and Mr. <PERSON><PERSON>", "Who Can It Be Now?", "Basket Case", "<PERSON> Good Johnny"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "What was the name of singer <PERSON>&#039;s second studio album, which was released on August 20, 2016?", "correct_answer": "Blonde", "incorrect_answers": ["<PERSON><PERSON><PERSON>", "Black", "Burgundy"], "all_answers": ["Blonde", "<PERSON><PERSON><PERSON>", "Black", "Burgundy"], "correct_answer_index": 0}], "Entertainment: Television": [{"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "Which actor portrays &quot;<PERSON>&quot; in the series &quot;Breaking Bad&quot;?", "correct_answer": " <PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "all_answers": [" <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "On the NBC show Community, whose catch-phrase was &quot;Pop! Pop!&quot;?", "correct_answer": "Magnitude", "incorrect_answers": ["Star Burns", "<PERSON>", "<PERSON><PERSON>"], "all_answers": ["Star Burns", "<PERSON>", "Magnitude", "<PERSON><PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "British actor <PERSON> stars as which role in &quot;The Walking Dead&quot;?", "correct_answer": "The Governor", "incorrect_answers": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON><PERSON>", "<PERSON>", "The Governor", "<PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "How many seasons did the Sci-Fi television show &quot;Stargate Universe&quot; have?", "correct_answer": "2", "incorrect_answers": ["10", "5", "3"], "all_answers": ["2", "10", "5", "3"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "In which state of America was the Fresh Prince of Bel-Air born and raised in?", "correct_answer": "Pennsylvania", "incorrect_answers": ["Florida", "California", "New Jersey"], "all_answers": ["Florida", "Pennsylvania", "California", "New Jersey"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "In Two and a Half Men, what is <PERSON>&#039;s son&#039;s name?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "In the show, <PERSON>, what does T.A.R.D.I.S stand for?", "correct_answer": "Time And Relative Dimensions In Space", "incorrect_answers": ["Time And Resting Dimensions In Space", "Time And Relative Dimensions In Style", "Toilet Aid Rope Dog Is Soup"], "all_answers": ["Time And Resting Dimensions In Space", "Time And Relative Dimensions In Style", "Toilet Aid Rope Dog Is Soup", "Time And Relative Dimensions In Space"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "In &quot;Star Trek: Voyager&quot;, which episode did Voyager establish real-time communication with Starfleet Headquarters?", "correct_answer": "Pathfinder", "incorrect_answers": ["Message In A Bottle", "Someone To Watch Over Me", "Counterpoint"], "all_answers": ["Message In A Bottle", "Someone To Watch Over Me", "Counterpoint", "Pathfinder"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "What is the real name of the famous spanish humorist, <PERSON> Risitas?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "In the Star Trek universe, what color is Vulcan blood?", "correct_answer": "Green", "incorrect_answers": ["Blue", "Red", "Purple"], "all_answers": ["Blue", "Red", "Green", "Purple"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "Which Star Trek race advances itself through assimilation?", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["Vulcan", "Betazoid", "Klingon"], "all_answers": ["Vulcan", "Betazoid", "<PERSON><PERSON>", "Klingon"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "Who was the first ever actor to play &quot;The Doctor&quot; on &quot;Doctor Who&quot;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "In the television show Breaking Bad, what is the street name of <PERSON> and Jesse&#039;s notorious product?", "correct_answer": "Blue Sky", "incorrect_answers": ["Baby <PERSON>", "Rock Candy", "Pure Glass"], "all_answers": ["Blue Sky", "Baby <PERSON>", "Rock Candy", "Pure Glass"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "What was the name of the the first episode of Doctor Who to air in 1963?", "correct_answer": "An Unearthly Child", "incorrect_answers": ["The Daleks", "The Aztecs", "The Edge of Destruction"], "all_answers": ["The Daleks", "The Aztecs", "An Unearthly Child", "The Edge of Destruction"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "What was the name of the teenage witch played by <PERSON><PERSON> who lives with her witch aunts <PERSON> and <PERSON><PERSON><PERSON>?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "all_answers": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "Which of these characters in &quot;Stranger Things&quot; has the power of Telekines<PERSON>?", "correct_answer": "Eleven", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "Eleven", "<PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "Which of these Bojack Horseman characters is a human?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON><PERSON>", "Princess <PERSON>", "<PERSON>-G<PERSON>bo"], "all_answers": ["<PERSON><PERSON>", "Princess <PERSON>", "<PERSON>", "<PERSON>-G<PERSON>bo"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "What is the name of <PERSON>&#039;s brother in &quot;Everybody Hates Chris&quot;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "When did the TV show <PERSON> and <PERSON><PERSON><PERSON> first air on Adult Swim?", "correct_answer": "2013", "incorrect_answers": ["2014", "2016", "2015"], "all_answers": ["2014", "2013", "2016", "2015"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "Which of the following awards do <PERSON> and <PERSON> NOT have?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "Grammy"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "Grammy"], "correct_answer_index": 0}], "Entertainment: Video Games": [{"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "Which Final Fantasy game consisted of a female-only cast of party members?", "correct_answer": "Final Fantasy X-2", "incorrect_answers": ["Final Fantasy IX", "Final Fantasy V", "Final Fantasy XIII-2"], "all_answers": ["Final Fantasy X-2", "Final Fantasy IX", "Final Fantasy V", "Final Fantasy XIII-2"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "Who was the voice actor for <PERSON> in Metal Gear Solid V: The Phantom Pain?", "correct_answer": "<PERSON><PERSON><PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "Which student in Yandere Simulator is known for asking irritating and stupid questions?", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "all_answers": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "Which of these is NOT a Humongous Entertainment game franchise?", "correct_answer": "Commander <PERSON><PERSON>", "incorrect_answers": ["<PERSON><PERSON><PERSON>", "Putt-Putt", "<PERSON><PERSON>"], "all_answers": ["<PERSON><PERSON><PERSON>", "Putt-Putt", "Commander <PERSON><PERSON>", "<PERSON><PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "Who is the main character in Metal Gear Rising: Revengeance?", "correct_answer": "Raiden", "incorrect_answers": ["Solid Snake", "Otacon", "<PERSON><PERSON><PERSON>"], "all_answers": ["Solid Snake", "Otacon", "<PERSON><PERSON><PERSON>", "Raiden"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "Which of the following is not a character in the video game Doki Doki Literature Club?", "correct_answer": "Nico", "incorrect_answers": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "all_answers": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Nico", "<PERSON><PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "What vehicle in PUBG has the highest top speed?", "correct_answer": "Motorcycle", "incorrect_answers": ["PG-117", "Dacia", "Buggy"], "all_answers": ["PG-117", "Dacia", "Motorcycle", "Buggy"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "Which Elite Four member from the first generation of Pok&eacute;mon became the champion in the next generation?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "What is the code name of <PERSON><PERSON> the cat in Persona 5?", "correct_answer": "Mona", "incorrect_answers": ["Panther", "Nightstalker", "<PERSON><PERSON><PERSON>"], "all_answers": ["Mona", "Panther", "Nightstalker", "<PERSON><PERSON><PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "What is the first weapon you acquire in Half-Life?", "correct_answer": "A crowbar", "incorrect_answers": ["A pistol", "The H.E.V suit", "Your fists"], "all_answers": ["A crowbar", "A pistol", "The H.E.V suit", "Your fists"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "What video game engine does the videogame Quake 2 run in?", "correct_answer": "iD Tech 2", "incorrect_answers": ["iD Tech 3", "iD Tech 1", "Unreal Engine"], "all_answers": ["iD Tech 3", "iD Tech 1", "Unreal Engine", "iD Tech 2"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "The starting pistol of the Terrorist team in a competitive match of Counter Strike: Global Offensive is what?", "correct_answer": "Glock-18", "incorrect_answers": ["Tec-9", "Desert Eagle", "Dual Berretas"], "all_answers": ["Tec-9", "Desert Eagle", "Dual Berretas", "Glock-18"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "In what year was &quot;Antichamber&quot; released?", "correct_answer": "2013", "incorrect_answers": ["2012", "2014", "2011"], "all_answers": ["2012", "2014", "2013", "2011"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "How many differently shaped Tetris pieces are there?", "correct_answer": "7", "incorrect_answers": ["5", "6", "8"], "all_answers": ["5", "6", "8", "7"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "TF2: What code does <PERSON> put into the door keypad in &quot;Meet the Spy&quot;?", "correct_answer": "1111", "incorrect_answers": ["1432", "1337", "No code"], "all_answers": ["1432", "1337", "No code", "1111"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "In what year was &quot;Metal Gear Solid&quot; released in North America?", "correct_answer": "1998", "incorrect_answers": ["1987", "2001", "2004"], "all_answers": ["1987", "2001", "1998", "2004"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "What is the name of the largest planet in Kerbal Space Program?", "correct_answer": "Jo<PERSON>", "incorrect_answers": ["<PERSON><PERSON><PERSON>", "Kerbol", "<PERSON><PERSON>"], "all_answers": ["<PERSON><PERSON><PERSON>", "Kerbol", "<PERSON><PERSON>", "Jo<PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "What is the maximum HP in Terraria?", "correct_answer": "500", "incorrect_answers": ["400", "1000", "100"], "all_answers": ["400", "500", "1000", "100"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "The &quot;Day of Defeat&quot; series of games take place during which war?", "correct_answer": "World War II", "incorrect_answers": ["World War I", "Vietnam War", "Iraq War"], "all_answers": ["World War I", "Vietnam War", "Iraq War", "World War II"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "Who is the main character of the game Half-Life: Opposing Force?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "Alyx Vance", "<PERSON>"], "all_answers": ["<PERSON>", "Alyx Vance", "<PERSON>", "<PERSON>"], "correct_answer_index": 3}], "Science & Nature": [{"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "Which of the following is not one of the groups on the periodic table?", "correct_answer": "Fluorines", "incorrect_answers": ["Alkali Metals", "Halogens", "<PERSON>"], "all_answers": ["Alkali Metals", "Fluorines", "Halogens", "<PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "Which of these Elements is a metalloid?", "correct_answer": "Antimony", "incorrect_answers": ["Tin", "Bromine", "Rubidium"], "all_answers": ["Tin", "Bromine", "Rubidium", "Antimony"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "Which of the following blood vessels carries deoxygenated blood?", "correct_answer": "Pulmonary Artery", "incorrect_answers": ["Pul<PERSON><PERSON>ein", "<PERSON><PERSON><PERSON>", "Coronary Artery"], "all_answers": ["Pul<PERSON><PERSON>ein", "<PERSON><PERSON><PERSON>", "Coronary Artery", "Pulmonary Artery"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "What is the official name of the star located closest to the North Celestial Pole?", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["<PERSON><PERSON><PERSON>", "Gamma Cephei", "Iota Cephei"], "all_answers": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Gamma Cephei", "Iota Cephei"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "What does DNA stand for?", "correct_answer": "Deoxyribonucleic Acid", "incorrect_answers": ["Deoxyribogenetic Acid", "Deoxyribogenetic Atoms", "Detoxic Acid"], "all_answers": ["Deoxyribogenetic Acid", "Deoxyribogenetic Atoms", "Deoxyribonucleic Acid", "Detoxic Acid"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "Which noble gas has the lowest atomic number?", "correct_answer": "Helium", "incorrect_answers": ["Neon", "Argon", "Krypton"], "all_answers": ["Neon", "Argon", "Helium", "Krypton"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "What does LASER stand for?", "correct_answer": "Light amplification by stimulated emission of radiation", "incorrect_answers": ["Lite analysing by stereo ecorazer", "Light amplifier by standby energy of radio", "Life antimatter by standing entry of range"], "all_answers": ["Lite analysing by stereo ecorazer", "Light amplifier by standby energy of radio", "Light amplification by stimulated emission of radiation", "Life antimatter by standing entry of range"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "Which gas forms about 78% of the Earth&rsquo;s atmosphere?", "correct_answer": "Nitrogen", "incorrect_answers": ["Oxygen", "Argon", "Carbon Dioxide"], "all_answers": ["Oxygen", "Argon", "Nitrogen", "Carbon Dioxide"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "What is the primary addictive substance found in tobacco?", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["Cat<PERSON>ne", "Ephedrine", "<PERSON><PERSON><PERSON>"], "all_answers": ["Cat<PERSON>ne", "Ephedrine", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "What is the largest animal currently on Earth?", "correct_answer": "Blue Whale", "incorrect_answers": ["Orca", "Colossal Squid", "Giraffe"], "all_answers": ["Orca", "Blue Whale", "Colossal Squid", "Giraffe"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "How many laws of thermodynamics are there?", "correct_answer": "Four", "incorrect_answers": ["Three", "Two", "Five"], "all_answers": ["Four", "Three", "Two", "Five"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "71% of the Earth&#039;s surface is made up of", "correct_answer": "Water", "incorrect_answers": ["Deserts", "Continents", "Forests"], "all_answers": ["Deserts", "Continents", "Forests", "Water"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "The biggest distinction between a eukaryotic cell and a prokaryotic cell is:", "correct_answer": "The presence or absence of a nucleus", "incorrect_answers": ["The overall size", "The presence or absence of certain organelles", "The mode of reproduction"], "all_answers": ["The overall size", "The presence or absence of certain organelles", "The mode of reproduction", "The presence or absence of a nucleus"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "What is the elemental symbol for mercury?", "correct_answer": "Hg", "incorrect_answers": ["Me", "Mc", "Hy"], "all_answers": ["Me", "Hg", "Mc", "Hy"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "What is the chemical makeup of water?", "correct_answer": "H20", "incorrect_answers": ["C12H6O2", "CO2", "H"], "all_answers": ["H20", "C12H6O2", "CO2", "H"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "Which is the most abundant element in the universe?", "correct_answer": "Hydrogen", "incorrect_answers": ["Helium", "Lithium", "Oxygen"], "all_answers": ["Hydrogen", "Helium", "Lithium", "Oxygen"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "What is the common name of the chemical compound &quot;dihydrogen monoxide&quot;?", "correct_answer": "Water", "incorrect_answers": ["Methane", "Ammonia ", "Laughing Gas"], "all_answers": ["Water", "Methane", "Ammonia ", "Laughing Gas"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "How many planets are in our Solar System?", "correct_answer": "Eight", "incorrect_answers": ["Nine", "Seven", "Ten"], "all_answers": ["Nine", "Seven", "Eight", "Ten"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "What was the name of the first artificial Earth satellite, launched by the Soviet Union in 1957?", "correct_answer": "Sputnik 1", "incorrect_answers": ["Soyuz 7K-OK", "Zenit-2", "Voskhod 3KV"], "all_answers": ["Soyuz 7K-OK", "Sputnik 1", "Zenit-2", "Voskhod 3KV"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "What did <PERSON> use to test genetic crossovers?", "correct_answer": "Peas", "incorrect_answers": ["Cats", "Flowers", "Parrots"], "all_answers": ["Cats", "Flowers", "Parrots", "Peas"], "correct_answer_index": 3}], "Science: Computers": [{"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "What is the domain name for the country Tuvalu?", "correct_answer": ".tv", "incorrect_answers": [".tu", ".tt", ".tl"], "all_answers": [".tu", ".tv", ".tt", ".tl"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "On a standard American QWERTY keyboard, what symbol will you enter if you hold the shift key and press 1?", "correct_answer": "Exclamation Mark", "incorrect_answers": ["Dollar Sign", "Percent Sign", "Asterisk"], "all_answers": ["Dollar Sign", "Percent Sign", "Exclamation Mark", "Asterisk"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "What programming language was GitHub written in?", "correct_answer": "<PERSON>", "incorrect_answers": ["JavaScript", "Python", "<PERSON><PERSON>"], "all_answers": ["JavaScript", "<PERSON>", "Python", "<PERSON><PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "Which computer language would you associate Django framework with?", "correct_answer": "Python", "incorrect_answers": ["C#", "C++", "Java"], "all_answers": ["Python", "C#", "C++", "Java"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "Which SQL keyword is used to fetch data from a database?", "correct_answer": "SELECT", "incorrect_answers": ["INDEX", "VALUES", "EXEC"], "all_answers": ["INDEX", "SELECT", "VALUES", "EXEC"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "The Electron computer was released in Britain during 1983 for the home computing market, by which company? ", "correct_answer": "Acorn Computers", "incorrect_answers": ["Sinclair Research", "Amstrad PLC", "Commodore Business Machines"], "all_answers": ["Sinclair Research", "Amstrad PLC", "Acorn Computers", "Commodore Business Machines"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "In any programming language, what is the most common way to iterate through an array?", "correct_answer": "&#039;For&#039; loops", "incorrect_answers": ["&#039;If&#039; Statements", "&#039;Do-while&#039; loops", "&#039;While&#039; loops"], "all_answers": ["&#039;If&#039; Statements", "&#039;Do-while&#039; loops", "&#039;While&#039; loops", "&#039;For&#039; loops"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "The C programming language was created by this American computer scientist. ", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "al-Khw<PERSON>rizmī", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "al-Khw<PERSON>rizmī", "<PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "What does the computer software acronym JVM stand for?", "correct_answer": "Java Virtual Machine", "incorrect_answers": ["Java Vendor Machine", "Java Visual Machine", "Just Virtual Machine"], "all_answers": ["Java Vendor Machine", "Java Virtual Machine", "Java Visual Machine", "Just Virtual Machine"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "What is the most preferred image format used for logos in the Wikimedia database?", "correct_answer": ".svg", "incorrect_answers": [".png", ".jpeg", ".gif"], "all_answers": [".png", ".svg", ".jpeg", ".gif"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "How many kilobytes in one gigabyte (in decimal)?", "correct_answer": "1000000", "incorrect_answers": ["1024", "1000", "1048576"], "all_answers": ["1024", "1000000", "1000", "1048576"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "In computing, what does LAN stand for?", "correct_answer": "Local Area Network", "incorrect_answers": ["Long Antenna Node", "Light Access Node", "Land Address Navigation"], "all_answers": ["Long Antenna Node", "Light Access Node", "Land Address Navigation", "Local Area Network"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "The numbering system with a radix of 16 is more commonly referred to as ", "correct_answer": "Hexidecimal", "incorrect_answers": ["Binary", "Duodecimal", "Octal"], "all_answers": ["Hexidecimal", "Binary", "Duodecimal", "Octal"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "What is the code name for the mobile operating system Android 7.0?", "correct_answer": "Nougat", "incorrect_answers": ["Ice Cream Sandwich", "<PERSON><PERSON>", "Marshmallow"], "all_answers": ["Ice Cream Sandwich", "<PERSON><PERSON>", "Nougat", "Marshmallow"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "In computing, what does MIDI stand for?", "correct_answer": "Musical Instrument Digital Interface", "incorrect_answers": ["Musical Interface of Digital Instruments", "Modular Interface of Digital Instruments", "Musical Instrument Data Interface"], "all_answers": ["Musical Instrument Digital Interface", "Musical Interface of Digital Instruments", "Modular Interface of Digital Instruments", "Musical Instrument Data Interface"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "In the programming language Java, which of these keywords would you put on a variable to make sure it doesn&#039;t get modified?", "correct_answer": "Final", "incorrect_answers": ["Static", "Private", "Public"], "all_answers": ["Static", "Private", "Final", "Public"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "The programming language &#039;Swift&#039; was created to replace what other programming language?", "correct_answer": "Objective-C", "incorrect_answers": ["C#", "<PERSON>", "C++"], "all_answers": ["C#", "<PERSON>", "Objective-C", "C++"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "Which computer hardware device provides an interface for all other connected devices to communicate?", "correct_answer": "Motherboard", "incorrect_answers": ["Central Processing Unit", "Hard Disk Drive", "Random Access Memory"], "all_answers": ["Central Processing Unit", "Hard Disk Drive", "Motherboard", "Random Access Memory"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "When Gmail first launched, how much storage did it provide for your email?", "correct_answer": "1GB", "incorrect_answers": ["512MB", "5GB", "Unlimited"], "all_answers": ["512MB", "1GB", "5GB", "Unlimited"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "What does LTS stand for in the software market?", "correct_answer": "Long Term Support", "incorrect_answers": ["Long Taco Service", "Ludicrous Transfer Speed", "Ludicrous Turbo Speed"], "all_answers": ["Long Term Support", "Long Taco Service", "Ludicrous Transfer Speed", "Ludicrous Turbo Speed"], "correct_answer_index": 0}], "Sports": [{"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "This Canadian television sportscaster is known for his &quot;Hockey Night in Canada&quot; role, a commentary show during hockey games.", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON> ", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON> ", "<PERSON>", "<PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "Who won the UEFA Champions League in 2016?", "correct_answer": "Real Madrid C.F.", "incorrect_answers": ["FC Bayern Munich", "Atletico Madrid", "Manchester City F.C."], "all_answers": ["FC Bayern Munich", "Atletico Madrid", "Manchester City F.C.", "Real Madrid C.F."], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "In baseball, how many fouls are an out?", "correct_answer": "0", "incorrect_answers": ["5", "3", "2"], "all_answers": ["0", "5", "3", "2"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "How many players are there in an association football/soccer team?", "correct_answer": "11", "incorrect_answers": ["10", "9", "8"], "all_answers": ["10", "9", "8", "11"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "What was the final score of the Germany vs. Brazil 2014 FIFA World Cup match?", "correct_answer": "7 - 1", "incorrect_answers": ["0 - 1", "3 - 4", "16 - 0"], "all_answers": ["0 - 1", "3 - 4", "7 - 1", "16 - 0"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "Who did <PERSON> win the Champions League with?", "correct_answer": "Liverpool", "incorrect_answers": ["Real Madrid", "Chelsea", "Man City"], "all_answers": ["Liverpool", "Real Madrid", "Chelsea", "Man City"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "Who won the 2016 Formula 1 World Driver&#039;s Championship?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "How many soccer players should be on the field at the same time?", "correct_answer": "22", "incorrect_answers": ["20", "24", "26"], "all_answers": ["20", "22", "24", "26"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "The Rio 2016 Summer Olympics held it&#039;s closing ceremony on what date?", "correct_answer": "August 21", "incorrect_answers": ["August 23", "August 19", "August 17"], "all_answers": ["August 21", "August 23", "August 19", "August 17"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "Who won the 2015 Formula 1 World Championship?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "In golf, what name is given to a hole score of two under par?", "correct_answer": "Eagle", "incorrect_answers": ["<PERSON><PERSON>", "Bogey", "Albatross"], "all_answers": ["<PERSON><PERSON>", "Bogey", "Eagle", "Albatross"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "Which year did <PERSON><PERSON> won his first ever Formula One World Drivers&#039; Championship?", "correct_answer": "2009", "incorrect_answers": ["2010", "2007", "2006"], "all_answers": ["2009", "2010", "2007", "2006"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "Which wrestler won the 2019 Men&rsquo;s Royal Rumble?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "In the 2014 FIFA World Cup, what was the final score in the match Brazil - Germany?", "correct_answer": "1-7", "incorrect_answers": ["1-5", "1-6", "2-6"], "all_answers": ["1-7", "1-5", "1-6", "2-6"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "How many points did <PERSON><PERSON><PERSON> score in his first NBA game?", "correct_answer": "25", "incorrect_answers": ["19", "69", "41"], "all_answers": ["19", "69", "41", "25"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "What year did the New Orleans Saints win the Super Bowl?", "correct_answer": "2010", "incorrect_answers": ["2008", "2009", "2011"], "all_answers": ["2008", "2010", "2009", "2011"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "Which country hosted the 2020 Summer Olympics?", "correct_answer": "Japan", "incorrect_answers": ["China", "Australia", "Germany"], "all_answers": ["Japan", "China", "Australia", "Germany"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "When was the first official international game played?", "correct_answer": "1872", "incorrect_answers": ["1880", "1863", "1865"], "all_answers": ["1880", "1863", "1872", "1865"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "Who won the premier league title in the 2015-2016 season following a fairy tale run?", "correct_answer": "Leicester City", "incorrect_answers": ["Tottenham Hotspur", "Watford", "Stoke City"], "all_answers": ["Leicester City", "Tottenham Hotspur", "Watford", "Stoke City"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "Which NFL team plays for New England?", "correct_answer": "Patriots", "incorrect_answers": ["Chiefs", "Dolphins", "49ers"], "all_answers": ["Chiefs", "Patriots", "Dolphins", "49ers"], "correct_answer_index": 1}], "Geography": [{"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "What state is the largest state of the United States of America?", "correct_answer": "Alaska", "incorrect_answers": ["California", "Texas", "Washington"], "all_answers": ["California", "Alaska", "Texas", "Washington"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "Greenland is a part of which kingdom?", "correct_answer": "Denmark", "incorrect_answers": ["Sweden", "Norway", "United Kingdom"], "all_answers": ["Denmark", "Sweden", "Norway", "United Kingdom"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "Which ocean borders the west coast of the United States?", "correct_answer": "Pacific", "incorrect_answers": ["Atlantic", "Indian", "Arctic"], "all_answers": ["Atlantic", "Pacific", "Indian", "Arctic"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "All of the following are classified as Finno-Ugric languages EXCEPT:", "correct_answer": "<PERSON><PERSON><PERSON>", "incorrect_answers": ["Hungarian", "Finnish", "Estonian"], "all_answers": ["<PERSON><PERSON><PERSON>", "Hungarian", "Finnish", "Estonian"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "Which city is the capital of the United States of America?", "correct_answer": "Washington D.C", "incorrect_answers": ["Seattle", "Albany", "Los Angeles"], "all_answers": ["Washington D.C", "Seattle", "Albany", "Los Angeles"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "What is the capital of Denmark?", "correct_answer": "Copenhagen", "incorrect_answers": ["Aarhus", "Odense", "Aalborg"], "all_answers": ["Aarhus", "Odense", "Copenhagen", "Aalborg"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "What is the name of New Zealand&#039;s indigenous people?", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["Vikings", "Polynesians", "Samoans"], "all_answers": ["<PERSON><PERSON>", "Vikings", "Polynesians", "Samoans"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "What is the capital of Indonesia?", "correct_answer": "Jakarta", "incorrect_answers": ["Bandung", "Medan", "Palembang"], "all_answers": ["Jakarta", "Bandung", "Medan", "Palembang"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "What is the smallest country in the world?", "correct_answer": "Vatican City", "incorrect_answers": ["Maldives", "Monaco", "Malta"], "all_answers": ["Maldives", "Monaco", "Malta", "Vatican City"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "What is the capital of the US State of New York?", "correct_answer": "Albany", "incorrect_answers": ["Buffalo", "New York", "Rochester"], "all_answers": ["Buffalo", "New York", "Rochester", "Albany"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "What is the capital of South Korea?", "correct_answer": "Seoul", "incorrect_answers": ["Pyongyang", "Taegu", "Kitakyushu"], "all_answers": ["Seoul", "Pyongyang", "Taegu", "Kitakyushu"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "Which small country is located between the borders of France and Spain?", "correct_answer": "Andorra", "incorrect_answers": ["San Marino", "Vatican City", "Lichtenstein"], "all_answers": ["Andorra", "San Marino", "Vatican City", "Lichtenstein"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "Which US state has the highest population?", "correct_answer": "California", "incorrect_answers": ["New York", "Texas", "Florida"], "all_answers": ["California", "New York", "Texas", "Florida"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "How many countries does Mexico border?", "correct_answer": "3", "incorrect_answers": ["2", "4", "1"], "all_answers": ["2", "3", "4", "1"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "What is the capital of India?", "correct_answer": "New Delhi", "incorrect_answers": ["Beijing", "Montreal", "<PERSON>ithi"], "all_answers": ["New Delhi", "Beijing", "Montreal", "<PERSON>ithi"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "Which of these is NOT an Australian state or territory?", "correct_answer": "Alberta", "incorrect_answers": ["New South Wales", "Victoria", "Queensland"], "all_answers": ["New South Wales", "Victoria", "Alberta", "Queensland"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "What is Laos?", "correct_answer": "Country", "incorrect_answers": ["Region", "River", "City"], "all_answers": ["Region", "River", "City", "Country"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "Which of the following Arab countries does NOT have a flag containing only Pan-Arab colours?", "correct_answer": "Qatar", "incorrect_answers": ["Kuwait", "United Arab Emirates", "Jordan"], "all_answers": ["Kuwait", "United Arab Emirates", "Jordan", "Qatar"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "If soccer is called football in England, what is American football called in England?", "correct_answer": "American football", "incorrect_answers": ["Combball", "Handball", "Touchdown"], "all_answers": ["Combball", "American football", "Handball", "Touchdown"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "What is the only state in the United States that does not have a flag in a shape with 4 edges?", "correct_answer": "Ohio", "incorrect_answers": ["Florida", "Idaho", "New Mexico"], "all_answers": ["Florida", "Ohio", "Idaho", "New Mexico"], "correct_answer_index": 1}], "History": [{"type": "multiple", "difficulty": "easy", "category": "History", "question": "Who discovered Penicillin?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "What is the historical name of Sri Lanka?", "correct_answer": "Ceylon", "incorrect_answers": ["Myanmar", "Colombo", "Badulla"], "all_answers": ["Ceylon", "Myanmar", "Colombo", "Badulla"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "The idea of Socialism was articulated and advanced by whom?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "Who was among those killed in the 2010 Smolensk, Russia plane crash tragedy?", "correct_answer": "The Polish President", "incorrect_answers": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "The Polish President"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "When was Google founded?", "correct_answer": "September 4, 1998", "incorrect_answers": ["October 9, 1997", "December 12, 1989", "<PERSON><PERSON><PERSON> 7th, 2000"], "all_answers": ["September 4, 1998", "October 9, 1997", "December 12, 1989", "<PERSON><PERSON><PERSON> 7th, 2000"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "These two countries held a commonwealth from the 16th to 18th century.", "correct_answer": "Poland and Lithuania", "incorrect_answers": ["Hutu and Rwanda", "North Korea and South Korea", "Bangladesh and Bhutan"], "all_answers": ["Hutu and Rwanda", "Poland and Lithuania", "North Korea and South Korea", "Bangladesh and Bhutan"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "Who was the first ruler of Haiti after the country had gained its independence?", "correct_answer": "<PERSON><PERSON><PERSON>", "incorrect_answers": ["<PERSON><PERSON><PERSON>t Louvert<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON><PERSON><PERSON>t Louvert<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "On what street did the 1666 Great Fire of London start?", "correct_answer": "Pudding Lane", "incorrect_answers": ["Baker Street", "Houses of Parliament", "St Paul&#039;s Cathedral"], "all_answers": ["Pudding Lane", "Baker Street", "Houses of Parliament", "St Paul&#039;s Cathedral"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "How was Socrates executed?", "correct_answer": "Poison", "incorrect_answers": ["Decapitation", "Firing squad", "Crucifixion "], "all_answers": ["Poison", "Decapitation", "Firing squad", "Crucifixion "], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "How many manned moon landings have there been?", "correct_answer": "6", "incorrect_answers": ["1", "3", "7"], "all_answers": ["1", "3", "7", "6"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "A collection of Sanskrit hymns and verses known as the Vedas are sacred texts in what religion?", "correct_answer": "Hinduism", "incorrect_answers": ["Judaism", "Islam", "Buddhism"], "all_answers": ["Judaism", "Islam", "Buddhism", "Hinduism"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "What was <PERSON>&#039;s nickname?", "correct_answer": "The Red Baron", "incorrect_answers": ["The High Flying Ace", "The Blue Serpent ", "The Germany Gunner"], "all_answers": ["The Red Baron", "The High Flying Ace", "The Blue Serpent ", "The Germany Gunner"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "Which modern day country is the region that was known as Phrygia in ancient times?", "correct_answer": "Turkey", "incorrect_answers": ["Syria", "Greece", "Egypt"], "all_answers": ["Syria", "Greece", "Egypt", "Turkey"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "Which famous military commander marched an army, which included war elephants, over the Alps during the Second Punic War?", "correct_answer": "Hannibal", "incorrect_answers": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON> the Great", "<PERSON><PERSON><PERSON>"], "all_answers": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON> the Great", "<PERSON><PERSON><PERSON>", "Hannibal"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "Which German field marshal was known as the `Desert Fox`?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "Which one of these was not a beach landing site in the Invasion of Normandy?", "correct_answer": "Silver", "incorrect_answers": ["Gold", "Juno", "Sword"], "all_answers": ["Gold", "Silver", "Juno", "Sword"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "In what year was the M1911 pistol designed?", "correct_answer": "1911", "incorrect_answers": ["1907", "1899", "1917"], "all_answers": ["1907", "1899", "1917", "1911"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "Which famous world leader is famed for the saying, &quot;Let them eat cake&quot;, yet is rumored that he/she never said it at all?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "In what year did the nuclear power plant in Chernobyl melt down?", "correct_answer": "1986", "incorrect_answers": ["1991", "1975", "1998"], "all_answers": ["1991", "1975", "1986", "1998"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "Who rode on horseback to warn the Minutemen that the British were coming during the U.S. Revolutionary War?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 0}], "Entertainment: Japanese Anime & Manga": [{"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "<PERSON><PERSON><PERSON> in &quot;Re:Zero&quot; is the witch of what?", "correct_answer": "Envy", "incorrect_answers": ["Pride", "<PERSON><PERSON><PERSON>", "Wrath"], "all_answers": ["Pride", "<PERSON><PERSON><PERSON>", "Wrath", "Envy"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "In the anime &quot;My Hero Academia&quot;, which character is shown with the ability to manipulate gravity?", "correct_answer": "<PERSON><PERSON><PERSON>", "incorrect_answers": ["Bakugo", "<PERSON><PERSON>", "<PERSON><PERSON> "], "all_answers": ["Bakugo", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> "], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "In the 2012 animated film &quot;Wolf Children&quot;, what are the names of the wolf children?", "correct_answer": "Ame &amp; <PERSON><PERSON>", "incorrect_answers": ["Hana &amp; <PERSON><PERSON>", "Ame &amp; Hana", "Chuck &amp; Anna"], "all_answers": ["Hana &amp; <PERSON><PERSON>", "Ame &amp; Hana", "Ame &amp; <PERSON><PERSON>", "Chuck &amp; Anna"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "What is the name of <PERSON>&#039;s stand in Jojo&#039;s Bizarre Adventure Part 7, Steel Ball Run?", "correct_answer": "Dirty Deeds Done Dirt Cheap", "incorrect_answers": ["Filthy Acts Done For A Reasonable Price", "Civil War", "God Bless The USA"], "all_answers": ["Filthy Acts Done For A Reasonable Price", "Dirty Deeds Done Dirt Cheap", "Civil War", "God Bless The USA"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "What&#039;s the English Dub Name of &quot;Smile Precure&quot;?", "correct_answer": "Glitter Force", "incorrect_answers": ["Sparkle Girls", "Fairy Tale Patrol", "Power Princesses"], "all_answers": ["Sparkle Girls", "Fairy Tale Patrol", "Power Princesses", "Glitter Force"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "The characters of &quot;Log Horizon&quot; are trapped in what game?", "correct_answer": "Elder Tale", "incorrect_answers": ["Sword Art Online", "Tower Unite", "Yggdrasil"], "all_answers": ["Sword Art Online", "Elder Tale", "Tower Unite", "Yggdrasil"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "Which Pok&eacute;mon and it&#039;s evolutions were banned from appearing in a main role after the Episode 38 Incident?", "correct_answer": "The Porygon Line", "incorrect_answers": ["The Pikachu Line", "The Elekid Line", "The Magby Line"], "all_answers": ["The Pikachu Line", "The Elekid Line", "The Magby Line", "The Porygon Line"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "Who is the author of the manga series &quot;Astro Boy&quot;?", "correct_answer": "<PERSON><PERSON><PERSON>", "incorrect_answers": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "all_answers": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "Which part from the JoJo&#039;s Bizarre Adventure manga is about a horse race across America?", "correct_answer": "Part 7: Steel Ball Run", "incorrect_answers": ["Part 6: Stone Ocean", "Part 3: Stardust Crusaders", "Part 5: Golden Wind"], "all_answers": ["Part 6: Stone Ocean", "Part 3: Stardust Crusaders", "Part 7: Steel Ball Run", "Part 5: Golden Wind"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "What animation studio produced &quot;<PERSON><PERSON><PERSON>&quot;?", "correct_answer": "<PERSON><PERSON><PERSON>", "incorrect_answers": ["Kyoto Animation", "<PERSON><PERSON>", "A-1 Pictures"], "all_answers": ["Kyoto Animation", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "A-1 Pictures"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "What caused the titular mascot of Yo-Kai Watch, <PERSON><PERSON><PERSON>, to become a yokai?", "correct_answer": "Being run over by a truck", "incorrect_answers": ["Ate one too many chocobars", "Through a magical ritual", "When he put on the harmaki"], "all_answers": ["Ate one too many chocobars", "Through a magical ritual", "Being run over by a truck", "When he put on the harmaki"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "What is the last name of <PERSON> and <PERSON><PERSON><PERSON> in the Fullmetal Alchemist series.", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON><PERSON>", "Elwood"], "all_answers": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Elwood"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "Who is the main character with yellow hair in the anime Na<PERSON><PERSON>?", "correct_answer": "<PERSON><PERSON><PERSON>", "incorrect_answers": ["Ten Ten", "<PERSON>suke", "<PERSON><PERSON><PERSON>"], "all_answers": ["Ten Ten", "<PERSON><PERSON><PERSON>", "<PERSON>suke", "<PERSON><PERSON><PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "In 2013, virtual pop-star <PERSON><PERSON><PERSON> had a sponsorship with which pizza chain?", "correct_answer": "Domino&#039;s", "incorrect_answers": ["<PERSON>&#039;s", "Pizza Hut", "Sabarro&#039;s"], "all_answers": ["Domino&#039;s", "<PERSON>&#039;s", "Pizza Hut", "Sabarro&#039;s"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "The two main characters of &quot;No Game No Life&quot;, <PERSON><PERSON> and <PERSON><PERSON>, together go by what name?", "correct_answer": "Blank", "incorrect_answers": ["Immanity", "Disboard", "Warbeasts"], "all_answers": ["Blank", "Immanity", "Disboard", "Warbeasts"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "Who is the true moon princess in Sailor Moon?", "correct_answer": "<PERSON>", "incorrect_answers": ["Sailor <PERSON>", "<PERSON>", "Sailor <PERSON>"], "all_answers": ["Sailor <PERSON>", "<PERSON>", "<PERSON>", "Sailor <PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "Who is the main heroine of the anime, Full Metal Panic!", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["Teletha Testarossa", "<PERSON>", "<PERSON><PERSON><PERSON>"], "all_answers": ["<PERSON><PERSON>", "Teletha Testarossa", "<PERSON>", "<PERSON><PERSON><PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "What is the age of <PERSON> in Pokemon when he starts his journey?", "correct_answer": "10", "incorrect_answers": ["11", "12", "9"], "all_answers": ["11", "12", "9", "10"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "In the anime Seven Deadly Sins what is the name of one of the sins?", "correct_answer": "<PERSON>", "incorrect_answers": ["Sakura", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "all_answers": ["Sakura", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "What color is <PERSON><PERSON><PERSON><PERSON>&#039;s half of the Scissor Blade in the anime Kill la KIll?", "correct_answer": "Red", "incorrect_answers": ["Green", "Blue", "Purple"], "all_answers": ["Green", "Blue", "Purple", "Red"], "correct_answer_index": 3}], "Entertainment: Cartoon & Animations": [{"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "Who voices for <PERSON> in the animated series RWBY?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "Which of the following did not feature in the cartoon &#039;Wacky Races&#039;?", "correct_answer": "The Dragon Wagon", "incorrect_answers": ["The Bouldermobile", "The Crimson Haybailer", "The Compact Pussycat"], "all_answers": ["The Bouldermobile", "The Crimson Haybailer", "The Dragon Wagon", "The Compact Pussycat"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "Which of these is NOT a catchphrase used by <PERSON> in the TV show &quot;<PERSON> and <PERSON><PERSON><PERSON>&quot;?", "correct_answer": "Slam dunk, nothing but net!", "incorrect_answers": ["Hit the sack, <PERSON>!", "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>-<PERSON>, bi<PERSON>!", "Wubba-lubba-dub-dub!"], "all_answers": ["Hit the sack, <PERSON>!", "Slam dunk, nothing but net!", "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>-<PERSON>, bi<PERSON>!", "Wubba-lubba-dub-dub!"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "In the show &quot;Foster&#039;s Home For Imaginary Friends&quot;, which character had an obsession with basketball?", "correct_answer": "Wilt", "incorrect_answers": ["Coco", "<PERSON>", "Cheese"], "all_answers": ["Wilt", "Coco", "<PERSON>", "Cheese"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "In the show &quot;<PERSON>&quot;, who are the main two employees of The Big Donut?", "correct_answer": "<PERSON> and <PERSON>", "incorrect_answers": ["<PERSON> and <PERSON>", "<PERSON> and <PERSON>", "<PERSON> and <PERSON>"], "all_answers": ["<PERSON> and <PERSON>", "<PERSON> and <PERSON>", "<PERSON> and <PERSON>", "<PERSON> and <PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "What is the name of the city that The Flintstones is based in?", "correct_answer": "Bedrock", "incorrect_answers": ["Stoneville", "Rockhampton", "Boulder City"], "all_answers": ["Bedrock", "Stoneville", "Rockhampton", "Boulder City"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "Who is the only voice actor to have a speaking part in all of the Disney Pixar feature films? ", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "In the Pixar film, &quot;Toy Story&quot; what was the name of the child who owned the toys?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "Which of these is NOT a Disney cartoon character?", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Mc<PERSON>uck"], "all_answers": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Mc<PERSON>uck"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "What was the name of the sea witch in the 1989 Disney film &quot;The Little Mermaid&quot;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON><PERSON>", "Maleficent", "<PERSON>"], "all_answers": ["<PERSON><PERSON>", "<PERSON>", "Maleficent", "<PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "Who created the Cartoon Network series &quot;Adventure Time&quot;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "Who was the villain of &#039;&#039;The Lion King&#039;&#039;?", "correct_answer": "Scar", "incorrect_answers": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Vada"], "all_answers": ["Scar", "<PERSON>", "<PERSON><PERSON><PERSON>", "Vada"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "What is lost in Hawaiian and is also the name of a little girl in a 2002 film which features a alien named &quot;Stitch&quot;?", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "Who created the Cartoon Network series &quot;Regular Show&quot;?", "correct_answer": "<PERSON><PERSON> <PERSON><PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "The song &#039;Little <PERSON>er&#039; features in which Disney cartoon film?", "correct_answer": "Bambi", "incorrect_answers": ["Cinderella", "Pinocchio", "The Jungle Book"], "all_answers": ["Bambi", "Cinderella", "Pinocchio", "The Jungle Book"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "Which of these characters from &quot;SpongeBob SquarePants&quot; is not a squid?", "correct_answer": "<PERSON>", "incorrect_answers": ["Orvillie", "Squidward", "Squidette"], "all_answers": ["Orvillie", "Squidward", "<PERSON>", "Squidette"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "What is Everest&#039;s favorite food in the Nickelodeon/Nick Jr. series &quot;PAW Patrol&quot;?", "correct_answer": "Liver", "incorrect_answers": ["Chicken", "Steak", "Caribou"], "all_answers": ["Chicken", "Steak", "Liver", "Caribou"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "What was the first Disney movie to use CGI?", "correct_answer": "The Black Cauldron", "incorrect_answers": ["Tron", "Toy Story", "Fantasia"], "all_answers": ["The Black Cauldron", "Tron", "Toy Story", "Fantasia"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "Which cartoon family lives in 742 Evergreen Terrace, Springfield USA?", "correct_answer": "The Simpsons", "incorrect_answers": ["The Hills", "The Flintstones", "The Griffins"], "all_answers": ["The Hills", "The Simpsons", "The Flintstones", "The Griffins"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "What is the relationship between <PERSON> and <PERSON><PERSON><PERSON> in the show &quot;<PERSON> and <PERSON><PERSON><PERSON>&quot;?", "correct_answer": "Grandfather and Grandson", "incorrect_answers": ["Father and Son", "Best Friends", "Crimefighting Partners"], "all_answers": ["Grandfather and Grandson", "Father and Son", "Best Friends", "Crimefighting Partners"], "correct_answer_index": 0}]}